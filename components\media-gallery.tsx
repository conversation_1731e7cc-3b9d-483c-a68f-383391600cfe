"use client";

import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { 
  Eye, 
  Download, 
  Trash2, 
  FileText, 
  Image as ImageIcon, 
  File, 
  Video, 
  Music,
  Archive,
  Grid3X3,
  List,
  ChevronLeft,
  ChevronRight
} from "lucide-react";
import { format } from "date-fns";
import { useGetMedia } from "@/features/media/api/use-get-media";
import { useDeleteMedia } from "@/features/media/api/use-delete-media";

interface MediaFile {
  id: string;
  fileName: string;
  originalFileName: string;
  mimeType: string;
  fileSize: number;
  url: string;
  category: string;
  entityType: string | null;
  entityId: string | null;
  metadata: Record<string, any> | null;
  createdAt: string;
}

interface MediaGalleryProps {
  category?: string;
  entityType?: string;
  entityId?: string;
  maxItems?: number;
  compact?: boolean;
  allowDelete?: boolean;
  showViewToggle?: boolean;
  onFileSelect?: (file: MediaFile) => void;
}

export function MediaGallery({
  category,
  entityType,
  entityId,
  maxItems,
  compact = false,
  allowDelete = true,
  showViewToggle = true,
  onFileSelect,
}: MediaGalleryProps) {
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [selectedFileIndex, setSelectedFileIndex] = useState<number>(0);
  const [isViewerOpen, setIsViewerOpen] = useState(false);

  const { data: mediaData, isLoading } = useGetMedia({
    category,
    entityType,
    entityId,
    limit: maxItems,
  });

  const deleteMutation = useDeleteMedia();

  const files = mediaData?.data || [];

  const getFileIcon = (mimeType: string, size: "sm" | "md" | "lg" = "md") => {
    const sizeClasses = {
      sm: "h-4 w-4",
      md: "h-6 w-6",
      lg: "h-8 w-8"
    };

    if (mimeType.startsWith('image/')) {
      return <ImageIcon className={`${sizeClasses[size]} text-blue-500`} />;
    }
    if (mimeType.startsWith('video/')) {
      return <Video className={`${sizeClasses[size]} text-purple-500`} />;
    }
    if (mimeType.startsWith('audio/')) {
      return <Music className={`${sizeClasses[size]} text-green-500`} />;
    }
    if (mimeType === 'application/pdf') {
      return <FileText className={`${sizeClasses[size]} text-red-500`} />;
    }
    if (mimeType.includes('zip') || mimeType.includes('rar') || mimeType.includes('tar')) {
      return <Archive className={`${sizeClasses[size]} text-orange-500`} />;
    }
    return <File className={`${sizeClasses[size]} text-gray-500`} />;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getCategoryColor = (category: string) => {
    const colors: Record<string, string> = {
      receipts: "bg-green-100 text-green-800 border-green-200",
      documents: "bg-blue-100 text-blue-800 border-blue-200",
      images: "bg-purple-100 text-purple-800 border-purple-200",
      general: "bg-gray-100 text-gray-800 border-gray-200",
    };
    return colors[category] || colors.general;
  };

  const handleFileClick = (file: MediaFile, index: number) => {
    if (onFileSelect) {
      onFileSelect(file);
    } else {
      setSelectedFileIndex(index);
      setIsViewerOpen(true);
    }
  };

  const handleDownload = (file: MediaFile) => {
    const link = document.createElement('a');
    link.href = file.url;
    link.download = file.originalFileName;
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleDelete = async (file: MediaFile) => {
    if (confirm(`Are you sure you want to delete "${file.originalFileName}"?`)) {
      try {
        await deleteMutation.mutateAsync(file.id);
      } catch (error) {
        // Error handling is done in the mutation
      }
    }
  };

  const nextFile = () => {
    setSelectedFileIndex((prev) => (prev + 1) % files.length);
  };

  const prevFile = () => {
    setSelectedFileIndex((prev) => (prev - 1 + files.length) % files.length);
  };

  const selectedFile = files[selectedFileIndex];

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-4">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
        <span className="ml-2 text-sm">Loading...</span>
      </div>
    );
  }

  if (files.length === 0) {
    return compact ? null : (
      <div className="text-center py-8 text-gray-500">
        <File className="h-12 w-12 mx-auto mb-2" />
        <p className="text-sm">No files found</p>
      </div>
    );
  }

  const renderCompactView = () => (
    <div className="flex gap-2 overflow-x-auto pb-2">
      {files.slice(0, maxItems || 6).map((file, index) => (
        <div 
          key={file.id} 
          className="flex-shrink-0 group cursor-pointer"
          onClick={() => handleFileClick(file, index)}
        >
          <div className="w-16 h-16 rounded-lg overflow-hidden bg-gray-100 flex items-center justify-center relative hover:ring-2 hover:ring-blue-500 transition-all">
            {file.mimeType.startsWith('image/') ? (
              <img
                src={file.url}
                alt={file.originalFileName}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="flex flex-col items-center">
                {getFileIcon(file.mimeType, "sm")}
              </div>
            )}
            <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all flex items-center justify-center">
              <Eye className="h-4 w-4 text-white opacity-0 group-hover:opacity-100 transition-opacity" />
            </div>
          </div>
        </div>
      ))}
      {files.length > (maxItems || 6) && (
        <div className="flex-shrink-0 w-16 h-16 rounded-lg border-2 border-dashed border-gray-300 flex items-center justify-center text-xs text-gray-500">
          +{files.length - (maxItems || 6)}
        </div>
      )}
    </div>
  );

  const renderGridView = () => (
    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3">
      {files.map((file, index) => (
        <Card key={file.id} className="group overflow-hidden hover:shadow-md transition-shadow cursor-pointer">
          <div 
            className="aspect-square bg-gray-50 flex items-center justify-center relative"
            onClick={() => handleFileClick(file, index)}
          >
            {file.mimeType.startsWith('image/') ? (
              <img
                src={file.url}
                alt={file.originalFileName}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="flex flex-col items-center gap-1">
                {getFileIcon(file.mimeType, "lg")}
                <span className="text-xs text-gray-500 text-center px-2 truncate max-w-full">
                  {file.originalFileName}
                </span>
              </div>
            )}
            
            <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-1">
              <Button size="sm" variant="secondary" className="h-8 w-8 p-0">
                <Eye className="h-3 w-3" />
              </Button>
              <Button size="sm" variant="secondary" className="h-8 w-8 p-0" onClick={(e) => {
                e.stopPropagation();
                handleDownload(file);
              }}>
                <Download className="h-3 w-3" />
              </Button>
              {allowDelete && (
                <Button size="sm" variant="destructive" className="h-8 w-8 p-0" onClick={(e) => {
                  e.stopPropagation();
                  handleDelete(file);
                }}>
                  <Trash2 className="h-3 w-3" />
                </Button>
              )}
            </div>
          </div>
          
          <CardContent className="p-2">
            <div className="space-y-1">
              <p className="text-xs font-medium truncate">{file.originalFileName}</p>
              <div className="flex items-center justify-between">
                <span className="text-xs text-gray-500">{formatFileSize(file.fileSize)}</span>
                <Badge variant="outline" className={`text-xs px-1 py-0 ${getCategoryColor(file.category)}`}>
                  {file.category}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );

  const renderListView = () => (
    <div className="space-y-2">
      {files.map((file, index) => (
        <div 
          key={file.id} 
          className="flex items-center gap-3 p-2 rounded-lg hover:bg-gray-50 cursor-pointer group"
          onClick={() => handleFileClick(file, index)}
        >
          <div className="flex-shrink-0">
            {file.mimeType.startsWith('image/') ? (
              <img
                src={file.url}
                alt={file.originalFileName}
                className="w-10 h-10 object-cover rounded"
              />
            ) : (
              getFileIcon(file.mimeType, "md")
            )}
          </div>
          
          <div className="flex-grow min-w-0">
            <p className="text-sm font-medium truncate">{file.originalFileName}</p>
            <div className="flex items-center gap-2 text-xs text-gray-500">
              <span>{formatFileSize(file.fileSize)}</span>
              <span>•</span>
              <span>{format(new Date(file.createdAt), 'MMM dd')}</span>
              <Badge variant="outline" className={`text-xs ${getCategoryColor(file.category)}`}>
                {file.category}
              </Badge>
            </div>
          </div>
          
          <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
            <Button size="sm" variant="ghost" className="h-8 w-8 p-0" onClick={(e) => {
              e.stopPropagation();
              handleDownload(file);
            }}>
              <Download className="h-3 w-3" />
            </Button>
            {allowDelete && (
              <Button size="sm" variant="ghost" className="h-8 w-8 p-0" onClick={(e) => {
                e.stopPropagation();
                handleDelete(file);
              }}>
                <Trash2 className="h-3 w-3" />
              </Button>
            )}
          </div>
        </div>
      ))}
    </div>
  );

  if (compact) {
    return renderCompactView();
  }

  return (
    <>
      <div className="space-y-4">
        {showViewToggle && (
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-600">
              {files.length} file{files.length !== 1 ? 's' : ''}
            </span>
            <div className="flex gap-1">
              <Button
                size="sm"
                variant={viewMode === "grid" ? "default" : "outline"}
                onClick={() => setViewMode("grid")}
                className="h-8 w-8 p-0"
              >
                <Grid3X3 className="h-3 w-3" />
              </Button>
              <Button
                size="sm"
                variant={viewMode === "list" ? "default" : "outline"}
                onClick={() => setViewMode("list")}
                className="h-8 w-8 p-0"
              >
                <List className="h-3 w-3" />
              </Button>
            </div>
          </div>
        )}
        
        {viewMode === "grid" ? renderGridView() : renderListView()}
      </div>

      {/* Enhanced File Viewer Modal */}
      <Dialog open={isViewerOpen} onOpenChange={setIsViewerOpen}>
        <DialogContent className="max-w-5xl max-h-[95vh] p-0">
          {selectedFile && (
            <>
              <DialogHeader className="p-4 pb-2">
                <div className="flex items-center justify-between">
                  <div className="min-w-0 flex-1">
                    <DialogTitle className="truncate">{selectedFile.originalFileName}</DialogTitle>
                    <DialogDescription className="flex items-center gap-3 mt-1">
                      <span>{formatFileSize(selectedFile.fileSize)}</span>
                      <span>•</span>
                      <span>{format(new Date(selectedFile.createdAt), 'MMM dd, yyyy HH:mm')}</span>
                      <Badge className={`${getCategoryColor(selectedFile.category)} text-xs`}>
                        {selectedFile.category}
                      </Badge>
                    </DialogDescription>
                  </div>
                  
                  <div className="flex items-center gap-2 ml-4">
                    {files.length > 1 && (
                      <>
                        <Button size="sm" variant="outline" onClick={prevFile}>
                          <ChevronLeft className="h-4 w-4" />
                        </Button>
                        <span className="text-sm text-gray-500">
                          {selectedFileIndex + 1} / {files.length}
                        </span>
                        <Button size="sm" variant="outline" onClick={nextFile}>
                          <ChevronRight className="h-4 w-4" />
                        </Button>
                      </>
                    )}
                  </div>
                </div>
              </DialogHeader>
              
              <div className="flex-1 p-4 pt-2 max-h-[75vh] overflow-auto">
                {selectedFile.mimeType.startsWith('image/') ? (
                  <img
                    src={selectedFile.url}
                    alt={selectedFile.originalFileName}
                    className="w-full h-auto max-h-full object-contain rounded"
                  />
                ) : selectedFile.mimeType.startsWith('video/') ? (
                  <video
                    src={selectedFile.url}
                    controls
                    className="w-full h-auto max-h-full rounded"
                  />
                ) : selectedFile.mimeType.startsWith('audio/') ? (
                  <div className="flex flex-col items-center justify-center py-12">
                    {getFileIcon(selectedFile.mimeType, "lg")}
                    <audio
                      src={selectedFile.url}
                      controls
                      className="mt-4 w-full max-w-md"
                    />
                  </div>
                ) : selectedFile.mimeType === 'application/pdf' ? (
                  <iframe
                    src={selectedFile.url}
                    className="w-full h-[60vh] rounded border"
                    title={selectedFile.originalFileName}
                  />
                ) : (
                  <div className="flex flex-col items-center justify-center py-12">
                    {getFileIcon(selectedFile.mimeType, "lg")}
                    <p className="mt-4 text-sm text-gray-500 text-center">
                      Preview not available for this file type
                    </p>
                  </div>
                )}
              </div>
              
              <div className="p-4 border-t flex justify-end gap-2">
                <Button variant="outline" onClick={() => handleDownload(selectedFile)}>
                  <Download className="h-4 w-4 mr-2" />
                  Download
                </Button>
                {allowDelete && (
                  <Button 
                    variant="destructive" 
                    onClick={() => {
                      handleDelete(selectedFile);
                      setIsViewerOpen(false);
                    }}
                    disabled={deleteMutation.isPending}
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete
                  </Button>
                )}
              </div>
            </>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
}