import { useMutation, useQueryClient } from "@tanstack/react-query";
import { client } from "@/lib/hono";
import { useCurrentUserId } from "@/lib/utils";
import { InferRequestType, InferResponseType } from "hono";

type ResponseType = InferResponseType<typeof client.api.wishlist.possibilities[":id"]["$patch"]>;
type RequestType = InferRequestType<typeof client.api.wishlist.possibilities[":id"]["$patch"]>["json"];

export const useEditWishlistPossibility = () => {
  const userId = useCurrentUserId();
  const queryClient = useQueryClient();

  const mutation = useMutation<ResponseType, Error, { id: string; possibility: RequestType }>({
    mutationFn: async ({ id, possibility }) => {
      if (!userId) {
        throw new Error("User not authenticated");
      }

      const response = await client.api.wishlist.possibilities[":id"].$patch(
        { param: { id }, json: possibility },
        {
          headers: {
            "X-User-ID": userId,
          },
        }
      );

      if (!response.ok) {
        throw new Error("Failed to update possibility");
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["wishlist-items", userId],
      });
      queryClient.invalidateQueries({
        queryKey: ["wishlist-item", userId],
      });
    },
  });

  return mutation;
};