import { useMutation, useQueryClient } from "@tanstack/react-query";
import { client } from "@/lib/hono";
import { useCurrentUserId } from "@/lib/utils";
import { InferRequestType, InferResponseType } from "hono";

type ResponseType = InferResponseType<typeof client.api.wishlist[":id"]["possibilities"]["$post"]>;
type RequestType = InferRequestType<typeof client.api.wishlist[":id"]["possibilities"]["$post"]>["json"];

export const useCreateWishlistPossibility = () => {
  const userId = useCurrentUserId();
  const queryClient = useQueryClient();

  const mutation = useMutation<ResponseType, Error, { wishlistItemId: string; possibility: RequestType }>({
    mutationFn: async ({ wishlistItemId, possibility }) => {
      if (!userId) {
        throw new Error("User not authenticated");
      }

      const response = await client.api.wishlist[":id"]["possibilities"].$post(
        { param: { id: wishlistItemId }, json: possibility },
        {
          headers: {
            "X-User-ID": userId,
          },
        }
      );

      if (!response.ok) {
        throw new Error("Failed to create possibility");
      }

      return response.json();
    },
    onSuccess: (data, { wishlistItemId }) => {
      queryClient.invalidateQueries({
        queryKey: ["wishlist-items", userId],
      });
      queryClient.invalidateQueries({
        queryKey: ["wishlist-item", userId, { id: wishlistItemId }],
      });
    },
  });

  return mutation;
};