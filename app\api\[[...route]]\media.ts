import { <PERSON>o } from "hono";
import { zValidator } from "@hono/zod-validator";
import { z } from "zod";
import { getServerUserId } from "@/lib/utils";
import { getS3Storage, type MediaUploadOptions } from "@/lib/s3-storage";
import { db } from "@/db/drizzle";
import { mediaFiles } from "@/db/schema";
import { createId } from "@paralleldrive/cuid2";
import { eq, and, desc } from "drizzle-orm";

const app = new Hono()
  .post(
    "/upload",
    async (c) => {
      try {
        const userId = await getServerUserId(c.req.raw.headers);
        
        // Get form data from request
        const formData = await c.req.formData();
        
        // Manually extract and validate form fields
        const category = (formData.get("category") as string) || "general";
        const entityType = formData.get("entityType") as string | null;
        const entityId = formData.get("entityId") as string | null;
        const metadata = formData.get("metadata") as string | null;
        
        // Validate category
        const validCategories = ["receipts", "documents", "images", "general"];
        if (!validCategories.includes(category)) {
          return c.json({ error: "Invalid category" }, 400);
        }
        
        const uploadedFiles: { file: File; id: string }[] = [];

        // Extract files from form data
        for (const [key, value] of formData.entries()) {
          if (key.startsWith('file') && value instanceof File) {
            uploadedFiles.push({
              file: value,
              id: createId(),
            });
          }
        }

        if (uploadedFiles.length === 0) {
          return c.json({ error: "No files provided" }, 400);
        }

        const s3Storage = getS3Storage();
        const uploadResults = [];

        for (const { file, id } of uploadedFiles) {
          try {
            // Upload to S3
            const uploadOptions: MediaUploadOptions = {
              userId,
              category: category as any,
              filename: `${id}-${file.name}`,
              contentType: file.type,
            };

            const s3Result = await s3Storage.uploadFile(file, file.name, uploadOptions);

            // Save to database
            const mediaFileId = createId();
            const [mediaFile] = await db
              .insert(mediaFiles)
              .values({
                id: mediaFileId,
                userId,
                fileName: uploadOptions.filename || file.name,
                originalFileName: file.name,
                mimeType: file.type,
                fileSize: file.size,
                s3Key: s3Result.key,
                s3Url: s3Result.url,
                category,
                entityType: entityType || null,
                entityId: entityId || null,
                metadata: metadata || null,
              })
              .returning();

            uploadResults.push({
              id: mediaFile.id,
              fileName: mediaFile.fileName,
              originalFileName: mediaFile.originalFileName,
              mimeType: mediaFile.mimeType,
              fileSize: mediaFile.fileSize,
              url: mediaFile.s3Url,
              category: mediaFile.category,
              entityType: mediaFile.entityType,
              entityId: mediaFile.entityId,
              createdAt: mediaFile.createdAt,
            });
          } catch (fileError) {
            console.error(`Error uploading file ${file.name}:`, fileError);
            // Continue with other files, but log the error
          }
        }

        if (uploadResults.length === 0) {
          return c.json({ error: "All file uploads failed" }, 500);
        }

        return c.json({ 
          data: uploadResults,
          message: `Successfully uploaded ${uploadResults.length} file(s)`
        });

      } catch (error) {
        console.error('Media upload error:', error);
        return c.json({ error: "Failed to upload media files" }, 500);
      }
    }
  )
  .get(
    "/",
    zValidator(
      "query",
      z.object({
        category: z.string().optional(),
        entityType: z.string().optional(),
        entityId: z.string().optional(),
        limit: z.string().transform(Number).optional().default("50"),
        offset: z.string().transform(Number).optional().default("0"),
      })
    ),
    async (c) => {
      try {
        const userId = await getServerUserId(c.req.raw.headers);
        const { category, entityType, entityId, limit, offset } = c.req.valid("query");

        // Build query conditions
        const conditions = [eq(mediaFiles.userId, userId)];
        
        if (category) {
          conditions.push(eq(mediaFiles.category, category));
        }
        
        if (entityType) {
          conditions.push(eq(mediaFiles.entityType, entityType));
        }
        
        if (entityId) {
          conditions.push(eq(mediaFiles.entityId, entityId));
        }

        const files = await db
          .select({
            id: mediaFiles.id,
            fileName: mediaFiles.fileName,
            originalFileName: mediaFiles.originalFileName,
            mimeType: mediaFiles.mimeType,
            fileSize: mediaFiles.fileSize,
            s3Key: mediaFiles.s3Key,
            s3Url: mediaFiles.s3Url,
            category: mediaFiles.category,
            entityType: mediaFiles.entityType,
            entityId: mediaFiles.entityId,
            metadata: mediaFiles.metadata,
            createdAt: mediaFiles.createdAt,
          })
          .from(mediaFiles)
          .where(and(...conditions))
          .orderBy(desc(mediaFiles.createdAt))
          .limit(limit)
          .offset(offset);

        // Generate signed URLs for each file
        const s3Storage = getS3Storage();
        const filesWithSignedUrls = await Promise.all(
          files.map(async (file) => {
            try {
              const signedUrl = await s3Storage.getPublicUrl(file.s3Key);
              return {
                ...file,
                url: signedUrl,
                metadata: file.metadata ? JSON.parse(file.metadata) : null,
              };
            } catch (error) {
              console.error(`Failed to generate signed URL for ${file.s3Key}:`, error);
              return {
                ...file,
                url: file.s3Url, // Fallback to stored URL
                metadata: file.metadata ? JSON.parse(file.metadata) : null,
              };
            }
          })
        );

        return c.json({ data: filesWithSignedUrls });
      } catch (error) {
        console.error('Media fetch error:', error);
        return c.json({ error: "Failed to fetch media files" }, 500);
      }
    }
  )
  .get(
    "/:id",
    zValidator("param", z.object({ id: z.string() })),
    async (c) => {
      try {
        const userId = await getServerUserId(c.req.raw.headers);
        const { id } = c.req.valid("param");

        const [file] = await db
          .select()
          .from(mediaFiles)
          .where(and(
            eq(mediaFiles.id, id),
            eq(mediaFiles.userId, userId)
          ));

        if (!file) {
          return c.json({ error: "File not found" }, 404);
        }

        // Generate signed URL for the file
        const s3Storage = getS3Storage();
        let signedUrl = file.s3Url; // Fallback to stored URL

        try {
          signedUrl = await s3Storage.getPublicUrl(file.s3Key);
        } catch (error) {
          console.error(`Failed to generate signed URL for ${file.s3Key}:`, error);
        }

        return c.json({
          data: {
            ...file,
            url: signedUrl,
            metadata: file.metadata ? JSON.parse(file.metadata) : null,
          }
        });
      } catch (error) {
        console.error('Media get error:', error);
        return c.json({ error: "Failed to get media file" }, 500);
      }
    }
  )
  .delete(
    "/:id",
    zValidator("param", z.object({ id: z.string() })),
    async (c) => {
      try {
        const userId = await getServerUserId(c.req.raw.headers);
        const { id } = c.req.valid("param");

        // Get file info first
        const [file] = await db
          .select()
          .from(mediaFiles)
          .where(and(
            eq(mediaFiles.id, id),
            eq(mediaFiles.userId, userId)
          ));

        if (!file) {
          return c.json({ error: "File not found" }, 404);
        }

        // Delete from S3
        const s3Storage = getS3Storage();
        try {
          await s3Storage.deleteFile(file.s3Key);
        } catch (s3Error) {
          console.warn(`Failed to delete file from S3: ${file.s3Key}`, s3Error);
          // Continue with database deletion even if S3 deletion fails
        }

        // Delete from database
        await db
          .delete(mediaFiles)
          .where(and(
            eq(mediaFiles.id, id),
            eq(mediaFiles.userId, userId)
          ));

        return c.json({ 
          data: { id },
          message: "File deleted successfully" 
        });
      } catch (error) {
        console.error('Media delete error:', error);
        return c.json({ error: "Failed to delete media file" }, 500);
      }
    }
  )
  .put(
    "/:id",
    zValidator("param", z.object({ id: z.string() })),
    zValidator(
      "json",
      z.object({
        fileName: z.string().optional(),
        category: z.enum(["receipts", "documents", "images", "general"]).optional(),
        entityType: z.string().optional(),
        entityId: z.string().optional(),
        metadata: z.record(z.any()).optional(),
      })
    ),
    async (c) => {
      try {
        const userId = await getServerUserId(c.req.raw.headers);
        const { id } = c.req.valid("param");
        const { fileName, category, entityType, entityId, metadata } = c.req.valid("json");

        // Check if file exists and belongs to user
        const [existingFile] = await db
          .select()
          .from(mediaFiles)
          .where(and(
            eq(mediaFiles.id, id),
            eq(mediaFiles.userId, userId)
          ));

        if (!existingFile) {
          return c.json({ error: "File not found" }, 404);
        }

        // Update file info
        const updateData: any = {
          updatedAt: new Date(),
        };

        if (fileName) updateData.fileName = fileName;
        if (category) updateData.category = category;
        if (entityType !== undefined) updateData.entityType = entityType;
        if (entityId !== undefined) updateData.entityId = entityId;
        if (metadata !== undefined) updateData.metadata = JSON.stringify(metadata);

        const [updatedFile] = await db
          .update(mediaFiles)
          .set(updateData)
          .where(and(
            eq(mediaFiles.id, id),
            eq(mediaFiles.userId, userId)
          ))
          .returning();

        return c.json({
          data: {
            ...updatedFile,
            metadata: updatedFile.metadata ? JSON.parse(updatedFile.metadata) : null,
          },
          message: "File updated successfully"
        });
      } catch (error) {
        console.error('Media update error:', error);
        return c.json({ error: "Failed to update media file" }, 500);
      }
    }
  );

export default app;