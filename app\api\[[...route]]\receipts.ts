import { Hono } from "hono";
import { z<PERSON>alida<PERSON> } from "@hono/zod-validator";
import { z } from "zod";
import { getServerUserId } from "@/lib/utils";
import { ReceiptProcessor, type ReceiptProcessingResult } from "@/lib/langgraph/receipt-processor";
import { db } from "@/db/drizzle";
import { accounts, categories, transactions, detailsTransactions } from "@/db/schema";
import { createId } from "@paralleldrive/cuid2";
import { eq } from "drizzle-orm";
import { convertAmountToMiliunits } from "@/lib/utils";
import * as fs from "fs";
import * as path from "path";
import { writeFile, mkdir } from "fs/promises";

const app = new Hono()
  .post(
    "/process",
    async (c) => {
      try {
        const userId = await getServerUserId(c.req.raw.headers);
        
        // Get form data from request
        const formData = await c.req.formData();
        const uploadedFiles: { file: File; id: string }[] = [];

        // Extract files from form data
        for (const [key, value] of formData.entries()) {
          if (key.startsWith('receipt-') && value instanceof File) {
            uploadedFiles.push({
              file: value,
              id: createId(),
            });
          }
        }

        if (uploadedFiles.length === 0) {
          return c.json({ error: "No receipt files provided" }, 400);
        }

        // Create temporary directory for uploaded files
        const tempDir = path.join(process.cwd(), 'temp', 'receipts');
        await mkdir(tempDir, { recursive: true });

        // Save files temporarily and create MediaItem objects
        const mediaItems = [];
        for (const { file, id } of uploadedFiles) {
          const fileExtension = path.extname(file.name) || '.jpg';
          const fileName = `receipt-${id}${fileExtension}`;
          const filePath = path.join(tempDir, fileName);
          
          // Save file to disk
          const arrayBuffer = await file.arrayBuffer();
          const buffer = Buffer.from(arrayBuffer);
          await writeFile(filePath, buffer);

          mediaItems.push({
            id,
            fileName: file.name,
            mimeType: file.type,
            url: '',
            filePath,
          });
        }

        // Get user's accounts and categories for processing
        const userAccounts = await db
          .select()
          .from(accounts)
          .where(eq(accounts.userId, userId));

        const userCategories = await db
          .select()
          .from(categories)
          .where(eq(categories.userId, userId));

        const userData = {
          accounts: userAccounts.map(acc => ({ id: acc.id, name: acc.name })),
          categories: userCategories.map(cat => ({ id: cat.id, name: cat.name })),
        };

        // Process receipts using the ReceiptProcessor with user data
        const processor = new ReceiptProcessor();
        const result = await processor.processReceipts(mediaItems, userData);

        console.log("Processing result:", JSON.stringify(result, null, 2));

        // Clean up temporary files
        for (const mediaItem of mediaItems) {
          try {
            fs.unlinkSync(mediaItem.filePath);
          } catch (error) {
            console.warn(`Failed to delete temporary file: ${mediaItem.filePath}`);
          }
        }

        // Prepare response with user context
        const response = {
          ...result,
          userAccounts: userData.accounts,
          userCategories: userData.categories,
        };

        return c.json({ data: response });

      } catch (error) {
        console.error('Receipt processing error:', error);
        return c.json({ error: "Failed to process receipts" }, 500);
      }
    },
  )
  .post(
    "/create-transactions",
    zValidator(
      "json",
      z.object({
        transactions: z.array(
          z.object({
            amount: z.number(),
            payee: z.string(),
            notes: z.string().optional(),
            date: z.string(),
            accountId: z.string(),
            categoryId: z.string().optional(),
            projectId: z.string().optional(),
            isIncome: z.boolean().default(false),
            detailsTransactions: z.array(
              z.object({
                name: z.string().optional(),
                quantity: z.number().optional(),
                unitPrice: z.number().optional(),
                amount: z.number(),
                categoryId: z.string().optional(),
                projectId: z.string().optional(),
                isIncome: z.boolean().default(false),
              })
            ),
          })
        ),
        newCategories: z.array(
          z.object({
            id: z.string(),
            name: z.string(),
            isNew: z.boolean(),
          })
        ).optional(),
      })
    ),
    async (c) => {
      try {
        const userId = await getServerUserId(c.req.raw.headers);
        const { transactions: receiptTransactions, newCategories } = c.req.valid("json");

        // Create new categories first if they exist
        const categoryIdMapping: { [oldId: string]: string } = {};
        const createdCategoriesCount = [];

        if (newCategories && newCategories.length > 0) {
          for (const newCategory of newCategories) {
            const categoryId = createId();
            const [createdCategory] = await db
              .insert(categories)
              .values({
                id: categoryId,
                name: newCategory.name,
                userId: userId,
              })
              .returning();
            
            categoryIdMapping[newCategory.id] = categoryId;
            createdCategoriesCount.push(createdCategory);
          }
        }

        const createdTransactions = [];

        for (const receiptTransaction of receiptTransactions) {
          // Verify the account belongs to the user
          const account = await db
            .select()
            .from(accounts)
            .where(eq(accounts.id, receiptTransaction.accountId))
            .then(rows => rows[0]);

          if (!account || account.userId !== userId) {
            return c.json({ error: "Invalid account ID" }, 400);
          }

          // Map category ID if it's a new category
          const mappedCategoryId = receiptTransaction.categoryId && categoryIdMapping[receiptTransaction.categoryId]
            ? categoryIdMapping[receiptTransaction.categoryId]
            : receiptTransaction.categoryId ?? null;

          // Create the main transaction with proper sign (negative for expenses)
          const transactionAmount = receiptTransaction.isIncome 
            ? convertAmountToMiliunits(receiptTransaction.amount)
            : -convertAmountToMiliunits(receiptTransaction.amount);

          const transactionId = createId();
          const [createdTransaction] = await db
            .insert(transactions)
            .values({
              id: transactionId,
              amount: transactionAmount,
              payee: receiptTransaction.payee,
              notes: receiptTransaction.notes ?? null,
              date: new Date(receiptTransaction.date),
              accountId: receiptTransaction.accountId,
              categoryId: mappedCategoryId,
              projectId: receiptTransaction.projectId ?? null,
            })
            .returning();

          // Create detail transactions
          const createdDetails = [];
          for (const detail of receiptTransaction.detailsTransactions) {
            // Map detail category ID if it's a new category
            const mappedDetailCategoryId = detail.categoryId && categoryIdMapping[detail.categoryId]
              ? categoryIdMapping[detail.categoryId]
              : detail.categoryId ?? null;

            // Apply proper sign for detail amounts (negative for expenses)
            const detailAmount = detail.isIncome 
              ? convertAmountToMiliunits(detail.amount)
              : -convertAmountToMiliunits(detail.amount);

            const [createdDetail] = await db
              .insert(detailsTransactions)
              .values({
                id: createId(),
                name: detail.name ?? null,
                quantity: detail.quantity ?? null,
                unitPrice: detail.unitPrice ? convertAmountToMiliunits(detail.unitPrice) : null,
                amount: detailAmount,
                transactionId: transactionId,
                categoryId: mappedDetailCategoryId,
                projectId: detail.projectId ?? null,
              })
              .returning();
            
            createdDetails.push(createdDetail);
          }

          createdTransactions.push({
            ...createdTransaction,
            detailsTransactions: createdDetails,
          });
        }

        return c.json({ 
          data: createdTransactions,
          message: `Successfully created ${createdTransactions.length} transactions${createdCategoriesCount.length > 0 ? ` and ${createdCategoriesCount.length} new categories` : ''}`
        });

      } catch (error) {
        console.error('Transaction creation error:', error);
        return c.json({ error: "Failed to create transactions" }, 500);
      }
    }
  );

export default app;