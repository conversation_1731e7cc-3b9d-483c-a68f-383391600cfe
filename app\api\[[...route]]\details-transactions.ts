import { Hono } from "hono";
import { detailsTransactions, mediaFiles } from "@/db/schema";
import { insertdetailsTransactionsSchema } from "@/db/schema";
import { zValidator } from "@hono/zod-validator";
import { z } from "zod";
import { db } from "@/db/drizzle";
import { createId } from "@paralleldrive/cuid2";
import { subDays, parse } from "date-fns";
import { transactions } from "@/db/schema";
import { accounts } from "@/db/schema";
import { categories } from "@/db/schema";
import { convertAmountFormMiliunits, getServerUserId } from "@/lib/utils";
import { and, eq, inArray, desc, lte, gte, sql } from "drizzle-orm";
import { S3Storage } from "@/lib/s3-storage";

const app = new Hono()
  .get(
    "/",
    zValidator(
      "query",
      z.object({
        from: z.string().optional(),
        to: z.string().optional(),
        accountId: z.string().optional(),
      }),
    ),
    async (c) => {
      try {
        const userId = await getServerUserId(c.req.raw.headers);
        const { from, to, accountId } = c.req.valid("query");

        const defaultTo = new Date();
        const defaultFrom = subDays(defaultTo, 30);

        const startDate = from
          ? parse(from, "yyyy-MM-dd", new Date())
          : defaultFrom;
        const endDate = to ? parse(to, "yyyy-MM-dd", new Date()) : defaultTo;

        let data = await db
          .select({
            id: detailsTransactions.id,
            name: detailsTransactions.name,
            quantity: detailsTransactions.quantity,
            unitPrice: detailsTransactions.unitPrice,
            amount: detailsTransactions.amount,
            date: transactions.date,
            transactionId: detailsTransactions.transactionId,
            categoryId: detailsTransactions.categoryId,
            category: categories.name,
            mediaFilesCount: sql<number>`count(${mediaFiles.id})`.as("mediaFilesCount"),
          })
          .from(detailsTransactions)
          .innerJoin(
            transactions,
            eq(detailsTransactions.transactionId, transactions.id),
          )
          .innerJoin(accounts, eq(transactions.accountId, accounts.id))
          .leftJoin(categories, eq(transactions.categoryId, categories.id))
          .leftJoin(
            mediaFiles,
            and(
              eq(mediaFiles.entityId, detailsTransactions.id),
              eq(mediaFiles.entityType, "detailsTransaction")
            )
          )
          .where(
            and(
              eq(detailsTransactions.transactionId, transactions.id),
              eq(accounts.userId, userId),
              gte(transactions.date, startDate),
              lte(transactions.date, endDate),
            ),
          )
          .groupBy(
            detailsTransactions.id,
            detailsTransactions.name,
            detailsTransactions.quantity,
            detailsTransactions.unitPrice,
            detailsTransactions.amount,
            transactions.date,
            detailsTransactions.transactionId,
            detailsTransactions.categoryId,
            categories.name
          );

        data = data.map((detail) => ({
          ...detail,
          amount: convertAmountFormMiliunits(detail.amount),
          unitPrice: convertAmountFormMiliunits(
            !detail.unitPrice ? 0 : detail.unitPrice,
          ),
        }));

        return c.json({ data });
      } catch (error) {
        return c.json({ error: "Unauthorized" }, 401);
      }
    },
  )
  .get(
    "/:id",
    zValidator(
      "param",
      z.object({
        id: z.string(),
      }),
    ),
    async (c) => {
      try {
        const userId = await getServerUserId(c.req.raw.headers);
        const { id } = c.req.valid("param");

        let [data] = await db
          .select({
            id: detailsTransactions.id,
            name: detailsTransactions.name,
            quantity: detailsTransactions.quantity,
            unitPrice: detailsTransactions.unitPrice,
            amount: detailsTransactions.amount,
            projectId: detailsTransactions.projectId,
            transactionId: detailsTransactions.transactionId,
            categoryId: detailsTransactions.categoryId,
          })
          .from(detailsTransactions)
          .innerJoin(
            transactions,
            eq(detailsTransactions.transactionId, transactions.id),
          )
          .innerJoin(accounts, eq(transactions.accountId, accounts.id))
          .leftJoin(categories, eq(transactions.categoryId, categories.id))
          .where(
            and(eq(detailsTransactions.id, id), eq(accounts.userId, userId)),
          );

        if (!data) {
          return c.json({ error: "Not found" }, 404);
        }

        data = {
          ...data,
          amount: convertAmountFormMiliunits(data.amount),
          unitPrice: convertAmountFormMiliunits(
            !data.unitPrice ? 0 : data.unitPrice,
          ),
        };

        return c.json({ data });
      } catch (error) {
        return c.json({ error: "Unauthorized" }, 401);
      }
    },
  )
  .post(
    "/",
    zValidator("json", insertdetailsTransactionsSchema.omit({ id: true })),
    async (c) => {
      try {
        const userId = await getServerUserId(c.req.raw.headers);
        const values = c.req.valid("json");

        const [data] = await db
          .insert(detailsTransactions)
          .values({
            id: createId(),
            ...values,
          })
          .returning();

        return c.json({ data });
      } catch (error) {
        return c.json({ error: "Unauthorized" }, 401);
      }
    },
  )
  .post(
    "/bulk-delete",
    zValidator(
      "json",
      z.object({
        ids: z.array(z.string()),
        transactionId: z.string().optional(),
      }),
    ),
    async (c) => {
      try {
        const userId = await getServerUserId(c.req.raw.headers);
        const values = c.req.valid("json");

        const data = await db
          .delete(detailsTransactions)
          .where(and(inArray(detailsTransactions.id, values.ids)))
          .returning({
            id: detailsTransactions.id,
          });
        return c.json({ data });
      } catch (error) {
        return c.json({ error: "Unauthorized" }, 401);
      }
    },
  )
  .post(
    "/bulk-create",
    zValidator(
      "json",
      z.array(
        insertdetailsTransactionsSchema.omit({
          id: true,
        }),
      ),
    ),
    async (c) => {
      try {
        const userId = await getServerUserId(c.req.raw.headers);
        const values = c.req.valid("json");

        const data = await db
          .insert(detailsTransactions)
          .values(
            values.map((value) => ({
              id: createId(),
              ...value,
            })),
          )
          .returning();

        return c.json({ data });
      } catch (error) {
        return c.json({ error: "Unauthorized" }, 401);
      }
    },
  )
  .patch(
    "/:id",
    zValidator(
      "param",
      z.object({
        id: z.string(),
      }),
    ),
    zValidator(
      "json",
      insertdetailsTransactionsSchema.omit({
        id: true,
      }),
    ),
    async (c) => {
      try {
        const userId = await getServerUserId(c.req.raw.headers);
        const { id } = c.req.valid("param");
        const values = c.req.valid("json");

        const transactionToUpdate = db
          .$with("deetailsTransactions_to_update")
          .as(
            db
              .select({ id: detailsTransactions.id })
              .from(detailsTransactions)
              .innerJoin(
                transactions,
                eq(transactions.id, detailsTransactions.transactionId),
              )
              .innerJoin(accounts, eq(transactions.accountId, accounts.id))
              .where(
                and(
                  eq(detailsTransactions.id, id),
                  eq(accounts.userId, userId),
                ),
              ),
          );

        const [data] = await db
          .with(transactionToUpdate)
          .update(detailsTransactions)
          .set(values)
          .where(
            inArray(
              detailsTransactions.id,
              sql`(select id from ${transactionToUpdate})`,
            ),
          )
          .returning();

        return c.json({ data });
      } catch (error) {
        return c.json({ error: "Unauthorized" }, 401);
      }
    },
  )
  .delete(
    "/:id",

    zValidator(
      "param",
      z.object({
        id: z.string(),
      }),
    ),

    async (c) => {
      try {
        const userId = await getServerUserId(c.req.raw.headers);
        const { id } = c.req.valid("param");

        if (!id) {
          return c.json(
            {
              error: "Missing id",
            },
            400,
          );
        }

        const transactionToDelete = db
          .$with("deetailsTransactions_to_update")
          .as(
            db
              .select({ id: detailsTransactions.id })
              .from(detailsTransactions)
              .innerJoin(
                transactions,
                eq(transactions.id, detailsTransactions.transactionId),
              )
              .innerJoin(accounts, eq(transactions.accountId, accounts.id))

              .where(
                and(
                  eq(detailsTransactions.id, id),
                  eq(accounts.userId, userId),
                ),
              ),
          );

        const [data] = await db
          .with(transactionToDelete)
          .delete(detailsTransactions)
          .where(
            inArray(
              detailsTransactions.id,
              sql`(select id from ${transactionToDelete})`,
            ),
          )
          .returning({
            id: detailsTransactions.id,
          });

        if (!data) {
          return c.json({ error: "Not found" }, 404);
        }
        return c.json({ data });
      } catch (error) {
        return c.json({ error: "Unauthorized" }, 401);
      }
    },
  )
  .post("/with-files", async (c) => {
    try {
      const userId = await getServerUserId(c.req.raw.headers);
      const formData = await c.req.formData();
      
      // Parse the data from form data
      const dataString = formData.get("data") as string;
      if (!dataString) {
        return c.json({ error: "Missing data" }, 400);
      }

      console.log("Raw dataString (create):", JSON.stringify(dataString));
      console.log("DataString length (create):", dataString.length);
      console.log("First 10 chars (create):", dataString.substring(0, 10));

      let detailsTransactionData;
      try {
        detailsTransactionData = JSON.parse(dataString);
      } catch (parseError) {
        console.error("JSON parse error (create):", parseError);
        console.error("Problematic string (create):", dataString);
        return c.json({
          error: "Invalid JSON data",
          details: parseError instanceof Error ? parseError.message : "Unknown parse error",
          receivedData: dataString.substring(0, 100) // First 100 chars for debugging
        }, 400);
      }
      
      // Create the details transaction first
      const [detailsTransaction] = await db
        .insert(detailsTransactions)
        .values({
          id: createId(),
          ...detailsTransactionData,
        })
        .returning();

      // Handle file uploads if present
      const files: File[] = [];
      for (const [key, value] of formData.entries()) {
        if (key.startsWith("file-") && value instanceof File) {
          files.push(value);
        }
      }

      if (files.length > 0) {
        const s3Storage = new S3Storage();
        
        for (const file of files) {
          try {
            const result = await s3Storage.uploadFile(file, file.name, {
              category: "detailsTransaction",
              entityType: "detailsTransaction",
              entityId: detailsTransaction.id,
            });

            // Save file metadata to database
            await db.insert(mediaFiles).values({
              id: createId(),
              userId,
              fileName: result.fileName,
              originalFileName: file.name,
              mimeType: file.type,
              fileSize: file.size,
              s3Key: result.s3Key,
              s3Url: result.url,
              category: "detailsTransaction",
              entityType: "detailsTransaction",
              entityId: detailsTransaction.id,
            });
          } catch (uploadError) {
            console.error("Failed to upload file:", uploadError);
          }
        }
      }

      return c.json({ 
        data: detailsTransaction, 
        message: "Details transaction created successfully" 
      });
    } catch (error) {
      console.error("Error creating details transaction with files:", error);
      return c.json({ error: "Failed to create details transaction" }, 500);
    }
  })
  .patch("/:id/with-files", async (c) => {
    try {
      const userId = await getServerUserId(c.req.raw.headers);
      const { id } = c.req.param();
      const formData = await c.req.formData();
      
      // Parse the data from form data
      const dataString = formData.get("data") as string;
      if (!dataString) {
        return c.json({ error: "Missing data" }, 400);
      }

      console.log("Raw dataString:", JSON.stringify(dataString));
      console.log("DataString length:", dataString.length);
      console.log("First 10 chars:", dataString.substring(0, 10));

      let detailsTransactionData;
      try {
        detailsTransactionData = JSON.parse(dataString);
      } catch (parseError) {
        console.error("JSON parse error:", parseError);
        console.error("Problematic string:", dataString);
        return c.json({
          error: "Invalid JSON data",
          details: parseError instanceof Error ? parseError.message : "Unknown parse error",
          receivedData: dataString.substring(0, 100) // First 100 chars for debugging
        }, 400);
      }

      // Verify ownership and update the details transaction
      const transactionToUpdate = db
        .$with("detailsTransactions_to_update")
        .as(
          db
            .select({ id: detailsTransactions.id })
            .from(detailsTransactions)
            .innerJoin(
              transactions,
              eq(transactions.id, detailsTransactions.transactionId),
            )
            .innerJoin(accounts, eq(transactions.accountId, accounts.id))
            .where(
              and(
                eq(detailsTransactions.id, id),
                eq(accounts.userId, userId),
              ),
            ),
        );

      const [detailsTransaction] = await db
        .with(transactionToUpdate)
        .update(detailsTransactions)
        .set(detailsTransactionData)
        .where(
          inArray(
            detailsTransactions.id,
            sql`(select id from ${transactionToUpdate})`,
          ),
        )
        .returning();

      if (!detailsTransaction) {
        return c.json({ error: "Details transaction not found" }, 404);
      }

      // Handle file uploads if present
      const files: File[] = [];
      for (const [key, value] of formData.entries()) {
        if (key.startsWith("file-") && value instanceof File) {
          files.push(value);
        }
      }

      if (files.length > 0) {
        const s3Storage = new S3Storage();
        
        for (const file of files) {
          try {
            const result = await s3Storage.uploadFile(file, file.name, {
              category: "detailsTransaction",
              entityType: "detailsTransaction",
              entityId: id,
            });

            // Save file metadata to database
            await db.insert(mediaFiles).values({
              id: createId(),
              userId,
              fileName: result.fileName,
              originalFileName: file.name,
              mimeType: file.type,
              fileSize: file.size,
              s3Key: result.s3Key,
              s3Url: result.url,
              category: "detailsTransaction",
              entityType: "detailsTransaction",
              entityId: id,
            });
          } catch (uploadError) {
            console.error("Failed to upload file:", uploadError);
          }
        }
      }

      return c.json({ 
        data: detailsTransaction, 
        message: "Details transaction updated successfully" 
      });
    } catch (error) {
      console.error("Error updating details transaction with files:", error);
      return c.json({ error: "Failed to update details transaction" }, 500);
    }
  });

export default app;
