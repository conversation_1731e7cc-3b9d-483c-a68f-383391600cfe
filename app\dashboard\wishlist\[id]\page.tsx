"use client";

import { useParams } from "next/navigation";
import { Skeleton } from "@/components/ui/skeleton";
import { Card, CardContent } from "@/components/ui/card";
import { useGetWishlistItem } from "@/features/wishlist/api/use-get-wishlist-item";
import { WishlistDetail } from "@/features/wishlist/components/wishlist-detail";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";

const WishlistDetailPage = () => {
  const params = useParams();
  const id = params.id as string;
  
  const { data: item, isLoading } = useGetWishlistItem(id);

  if (isLoading) {
    return (
      <div className="max-w-4xl mx-auto p-6 space-y-6">
        <div className="flex items-center gap-2">
          <Skeleton className="h-10 w-10" />
          <Skeleton className="h-8 w-48" />
        </div>
        <Card>
          <CardContent className="p-6">
            <div className="space-y-4">
              <Skeleton className="h-6 w-full" />
              <Skeleton className="h-6 w-3/4" />
              <Skeleton className="h-20 w-full" />
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!item) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="text-center py-12">
          <h2 className="text-2xl font-semibold mb-2">Wishlist item not found</h2>
          <p className="text-gray-600 mb-4">The wishlist item you're looking for doesn't exist.</p>
          <Link href="/dashboard/wishlist">
            <Button>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Wishlist
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="mb-6">
        <Link href="/dashboard/wishlist">
          <Button variant="ghost" className="mb-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Wishlist
          </Button>
        </Link>
      </div>
      
      <WishlistDetail item={item} />
    </div>
  );
};

export default WishlistDetailPage;